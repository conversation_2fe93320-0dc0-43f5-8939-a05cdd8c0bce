//
//  JigsawShapeGenerator.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

struct JigsawPieceInfo {
    let row: Int
    let col: Int
    let pieceID: Int
    let shape: UIBezierPath
    let imageRect: CGRect
    let hasTopTab: Bool
    let hasRightTab: Bool
    let hasBottomTab: Bool
    let hasLeftTab: Bool
}

class JigsawShapeGenerator {

    // MARK: - Properties
    private let pieceCount: Int
    private let imageSize: CGSize
    private let gridRows: Int
    private let gridCols: Int
    private let pieceWidth: CGFloat
    private let pieceHeight: CGFloat

    // Tab configuration - more realistic proportions
    private let tabSize: CGFloat = 30
    private let tabVariation: CGFloat = 0.4 // Random variation in tab size
    private let edgeVariation: CGFloat = 0.15 // Slight variation in straight edges

    // MARK: - Initialization
    init(pieceCount: Int, imageSize: CGSize) {
        self.pieceCount = pieceCount
        self.imageSize = imageSize

        // Calculate optimal grid dimensions
        let aspectRatio = imageSize.width / imageSize.height
        let idealCols = sqrt(Double(pieceCount) * Double(aspectRatio))
        let idealRows = sqrt(Double(pieceCount) / Double(aspectRatio))

        self.gridCols = max(2, Int(round(idealCols)))
        self.gridRows = max(2, Int(round(idealRows)))

        self.pieceWidth = imageSize.width / CGFloat(gridCols)
        self.pieceHeight = imageSize.height / CGFloat(gridRows)

        print("🧩 Jigsaw Generator: \(pieceCount) pieces in \(gridRows)x\(gridCols) grid")
        print("📏 Piece size: \(pieceWidth) x \(pieceHeight)")
    }

    // MARK: - Public Methods
    func generatePieces() -> [JigsawPieceInfo] {
        var pieces: [JigsawPieceInfo] = []
        var tabPattern = generateTabPattern()

        for row in 0..<gridRows {
            for col in 0..<gridCols {
                let pieceID = row * gridCols + col

                // Skip if we've reached the desired piece count
                if pieceID >= pieceCount {
                    break
                }

                let piece = createPiece(row: row, col: col, pieceID: pieceID, tabPattern: tabPattern)
                pieces.append(piece)
            }
        }

        return pieces
    }

    // MARK: - Private Methods
    private func generateTabPattern() -> [[Bool]] {
        // Generate random tab pattern for horizontal and vertical connections
        var horizontalTabs = Array(repeating: Array(repeating: false, count: gridCols - 1), count: gridRows)
        var verticalTabs = Array(repeating: Array(repeating: false, count: gridCols), count: gridRows - 1)

        // Randomly assign tabs
        for row in 0..<gridRows {
            for col in 0..<(gridCols - 1) {
                horizontalTabs[row][col] = Bool.random()
            }
        }

        for row in 0..<(gridRows - 1) {
            for col in 0..<gridCols {
                verticalTabs[row][col] = Bool.random()
            }
        }

        return [horizontalTabs.flatMap { $0 }, verticalTabs.flatMap { $0 }]
    }

    private func createPiece(row: Int, col: Int, pieceID: Int, tabPattern: [[Bool]]) -> JigsawPieceInfo {
        let x = CGFloat(col) * pieceWidth
        let y = CGFloat(row) * pieceHeight

        // Determine which sides have tabs
        let hasTopTab = row > 0 ? tabPattern[1][(row - 1) * gridCols + col] : false
        let hasBottomTab = row < gridRows - 1 ? tabPattern[1][row * gridCols + col] : false
        let hasLeftTab = col > 0 ? tabPattern[0][row * (gridCols - 1) + (col - 1)] : false
        let hasRightTab = col < gridCols - 1 ? tabPattern[0][row * (gridCols - 1) + col] : false

        // Create the piece shape
        let shape = createPieceShape(
            baseRect: CGRect(x: 0, y: 0, width: pieceWidth, height: pieceHeight),
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab
        )

        // Image rect for this piece
        let imageRect = CGRect(x: x, y: y, width: pieceWidth, height: pieceHeight)

        return JigsawPieceInfo(
            row: row,
            col: col,
            pieceID: pieceID,
            shape: shape,
            imageRect: imageRect,
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab
        )
    }

    private func createPieceShape(baseRect: CGRect, hasTopTab: Bool, hasRightTab: Bool, hasBottomTab: Bool, hasLeftTab: Bool) -> UIBezierPath {
        let path = UIBezierPath()

        // Start from top-left corner with slight variation
        let startPoint = addEdgeVariation(to: CGPoint(x: baseRect.minX, y: baseRect.minY))
        path.move(to: startPoint)

        // Top edge
        let topEndPoint = addEdgeVariation(to: CGPoint(x: baseRect.maxX, y: baseRect.minY))
        if hasTopTab {
            addRealisticTabToPath(path, from: startPoint, to: topEndPoint, isOutward: true, isHorizontal: true)
        } else {
            addVariedEdgeToPath(path, from: startPoint, to: topEndPoint)
        }

        // Right edge
        let rightEndPoint = addEdgeVariation(to: CGPoint(x: baseRect.maxX, y: baseRect.maxY))
        if hasRightTab {
            addRealisticTabToPath(path, from: topEndPoint, to: rightEndPoint, isOutward: true, isHorizontal: false)
        } else {
            addVariedEdgeToPath(path, from: topEndPoint, to: rightEndPoint)
        }

        // Bottom edge
        let bottomEndPoint = addEdgeVariation(to: CGPoint(x: baseRect.minX, y: baseRect.maxY))
        if hasBottomTab {
            addRealisticTabToPath(path, from: rightEndPoint, to: bottomEndPoint, isOutward: false, isHorizontal: true)
        } else {
            addVariedEdgeToPath(path, from: rightEndPoint, to: bottomEndPoint)
        }

        // Left edge
        if hasLeftTab {
            addRealisticTabToPath(path, from: bottomEndPoint, to: startPoint, isOutward: false, isHorizontal: false)
        } else {
            addVariedEdgeToPath(path, from: bottomEndPoint, to: startPoint)
        }

        path.close()
        return path
    }

    // MARK: - Helper Methods for Realistic Jigsaw Shapes

    private func addEdgeVariation(to point: CGPoint) -> CGPoint {
        let variation = edgeVariation
        let xOffset = CGFloat.random(in: -variation...variation)
        let yOffset = CGFloat.random(in: -variation...variation)
        return CGPoint(x: point.x + xOffset, y: point.y + yOffset)
    }

    private func addVariedEdgeToPath(_ path: UIBezierPath, from startPoint: CGPoint, to endPoint: CGPoint) {
        // Add slight curves to straight edges for more organic look
        let distance = sqrt(pow(endPoint.x - startPoint.x, 2) + pow(endPoint.y - startPoint.y, 2))
        let midPoint = CGPoint(x: (startPoint.x + endPoint.x) / 2, y: (startPoint.y + endPoint.y) / 2)

        // Add subtle curve variation
        let curveVariation = distance * 0.05 * CGFloat.random(in: -1...1)
        let isHorizontal = abs(endPoint.x - startPoint.x) > abs(endPoint.y - startPoint.y)

        let controlPoint: CGPoint
        if isHorizontal {
            controlPoint = CGPoint(x: midPoint.x, y: midPoint.y + curveVariation)
        } else {
            controlPoint = CGPoint(x: midPoint.x + curveVariation, y: midPoint.y)
        }

        path.addQuadCurve(to: endPoint, controlPoint: controlPoint)
    }

    private func addRealisticTabToPath(_ path: UIBezierPath, from startPoint: CGPoint, to endPoint: CGPoint, isOutward: Bool, isHorizontal: Bool) {
        let edgeLength = isHorizontal ? abs(endPoint.x - startPoint.x) : abs(endPoint.y - startPoint.y)

        // Calculate tab position with some randomness
        let tabPosition = 0.4 + CGFloat.random(in: -0.1...0.1) // 30-50% along the edge
        let tabCenterRatio = tabPosition

        // Tab size relative to edge length
        let baseTabSize = min(tabSize, edgeLength * 0.4)
        let randomVariation = 1.0 + CGFloat.random(in: -tabVariation...tabVariation)
        let actualTabSize = baseTabSize * randomVariation

        // Calculate points
        let tabDirection: CGFloat = isOutward ? 1 : -1

        var tabStartPoint: CGPoint
        var tabEndPoint: CGPoint
        var tabCenter: CGPoint

        if isHorizontal {
            let tabX = startPoint.x + (endPoint.x - startPoint.x) * tabCenterRatio
            let neckWidth = actualTabSize * 0.3

            tabStartPoint = CGPoint(x: tabX - neckWidth, y: startPoint.y)
            tabEndPoint = CGPoint(x: tabX + neckWidth, y: startPoint.y)
            tabCenter = CGPoint(x: tabX, y: startPoint.y + actualTabSize * tabDirection)
        } else {
            let tabY = startPoint.y + (endPoint.y - startPoint.y) * tabCenterRatio
            let neckWidth = actualTabSize * 0.3

            tabStartPoint = CGPoint(x: startPoint.x, y: tabY - neckWidth)
            tabEndPoint = CGPoint(x: startPoint.x, y: tabY + neckWidth)
            tabCenter = CGPoint(x: startPoint.x + actualTabSize * tabDirection, y: tabY)
        }

        // Draw edge to tab start
        addVariedEdgeToPath(path, from: startPoint, to: tabStartPoint)

        // Create organic tab shape using multiple curves
        createOrganicTab(path: path,
                        startPoint: tabStartPoint,
                        endPoint: tabEndPoint,
                        center: tabCenter,
                        size: actualTabSize,
                        isHorizontal: isHorizontal)

        // Draw from tab end to edge end
        addVariedEdgeToPath(path, from: tabEndPoint, to: endPoint)
    }

    private func createOrganicTab(path: UIBezierPath, startPoint: CGPoint, endPoint: CGPoint, center: CGPoint, size: CGFloat, isHorizontal: Bool) {
        // Create more organic, asymmetric tab shape
        let radius = size * 0.6
        let asymmetryFactor = CGFloat.random(in: 0.8...1.2)

        // Add slight randomness to make each tab unique
        let variation1 = CGFloat.random(in: -0.2...0.2)
        let variation2 = CGFloat.random(in: -0.2...0.2)

        if isHorizontal {
            // Create control points for organic curves
            let cp1 = CGPoint(x: startPoint.x + size * 0.1, y: startPoint.y + (center.y - startPoint.y) * (0.3 + variation1))
            let cp2 = CGPoint(x: center.x - radius * asymmetryFactor, y: center.y + radius * 0.2)
            let cp3 = CGPoint(x: center.x + radius * asymmetryFactor, y: center.y + radius * 0.2)
            let cp4 = CGPoint(x: endPoint.x - size * 0.1, y: endPoint.y + (center.y - endPoint.y) * (0.3 + variation2))

            // Draw organic tab with multiple curves
            path.addCurve(to: CGPoint(x: center.x - radius * 0.7, y: center.y),
                         controlPoint1: cp1,
                         controlPoint2: cp2)

            path.addCurve(to: CGPoint(x: center.x + radius * 0.7, y: center.y),
                         controlPoint1: CGPoint(x: center.x - radius * 0.3, y: center.y + radius * asymmetryFactor),
                         controlPoint2: CGPoint(x: center.x + radius * 0.3, y: center.y + radius * asymmetryFactor))

            path.addCurve(to: endPoint,
                         controlPoint1: cp3,
                         controlPoint2: cp4)
        } else {
            // Vertical tab
            let cp1 = CGPoint(x: startPoint.x + (center.x - startPoint.x) * (0.3 + variation1), y: startPoint.y + size * 0.1)
            let cp2 = CGPoint(x: center.x + radius * 0.2, y: center.y - radius * asymmetryFactor)
            let cp3 = CGPoint(x: center.x + radius * 0.2, y: center.y + radius * asymmetryFactor)
            let cp4 = CGPoint(x: endPoint.x + (center.x - endPoint.x) * (0.3 + variation2), y: endPoint.y - size * 0.1)

            path.addCurve(to: CGPoint(x: center.x, y: center.y - radius * 0.7),
                         controlPoint1: cp1,
                         controlPoint2: cp2)

            path.addCurve(to: CGPoint(x: center.x, y: center.y + radius * 0.7),
                         controlPoint1: CGPoint(x: center.x + radius * asymmetryFactor, y: center.y - radius * 0.3),
                         controlPoint2: CGPoint(x: center.x + radius * asymmetryFactor, y: center.y + radius * 0.3))

            path.addCurve(to: endPoint,
                         controlPoint1: cp3,
                         controlPoint2: cp4)
        }
    }
}
